import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/model/seller_account_balance/seller_account_balance_transactions_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class AccountBalanceTransactionCommonWidgets {
  //region Amount credit and debit against an order
  static Widget creditDebitAgainstOrder({required Transaction transaction}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ///Credit and debit icon
              Image.asset(
                transaction.transactionType == AppStrings.credited
                    ? AppImages.creditedAmount
                    : AppImages.debited,
                height: 30,
                width: 30,
                cacheWidth: 90,
                cacheHeight: 90,
              ),

              ///Heading and order number
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 10),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      ///Heading, Order number and date time
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ///Heading
                          Container(
                            alignment: Alignment.centerLeft,
                            height: 20,
                            child: Text(
                              transaction.transactionType == AppStrings.credited
                                  ? AppStrings.amountCreditedAgainst
                                  : AppStrings.amountDebitAgainst,
                              overflow: TextOverflow.visible,
                              style: AppTextStyle.contentHeading0(
                                  textColor: AppColors.writingBlack0),
                            ),
                          ),
                          verticalSizedBox(5),

                          ///Order number and date time
                          Container(
                            alignment: Alignment.centerLeft,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                ///Order number
                                Container(
                                  margin: const EdgeInsets.only(right: 10),
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 5),
                                  decoration: BoxDecoration(
                                      color: AppColors.textFieldFill2,
                                      borderRadius: BorderRadius.circular(60)),
                                  child: Container(
                                    alignment: Alignment.center,
                                    child: Text(
                                      transaction.orderNumber!,
                                      style: AppTextStyle.smallText(
                                          textColor: AppColors.writingBlack1),
                                    ),
                                  ),
                                ),

                                if (transaction.date != null)
                                  Text(
                                    CommonMethods.dateTimeAmPm(
                                        date: "${transaction.date!} 00:00:00")[1],
                                    style: AppTextStyle.smallText(
                                        textColor: AppColors.writingBlack1),
                                  )
                              ],
                            ),
                          )
                        ],
                      ),
                      verticalSizedBox(5),

                      ///Date and status
                    ],
                  ),
                ),
              ),

              ///Price and status
              Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    alignment: Alignment.center,
                    // height: 20,
                    child: Text(
                      "₹${transaction.payoutAmount}",
                      overflow: TextOverflow.visible,
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack),
                    ),
                  ),

                  ///Success and faild
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      transaction.transactionStatus == AppStrings.success
                          ? AppStrings.successMessage
                          : AppStrings.failMessage,
                      style: AppTextStyle.subTitle(
                          textColor: transaction.transactionStatus ==
                                  AppStrings.success
                              ? AppColors.brandBlack
                              : AppColors.red),
                    ),
                  )
                ],
              )
            ],
          ),
        ),
        divider()
      ],
    );
  }
  //endregion

  //region Withdrew
  static Widget withdrew({required Transaction transaction}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ///Credit and debit icon
              Image.asset(
                AppImages.withdrawRequest,
                height: 30,
                width: 30,
                cacheWidth: 90,
                cacheHeight: 90,
              ),

              ///Heading and order number
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 10),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      ///Heading, Order number and date time
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ///Heading
                          Container(
                            alignment: Alignment.centerLeft,
                            // height: 20,
                            child: Text(
                              transaction.notes ==
                                      "Added to store shipping balance"
                                  ? "${AppStrings.withdrawalRequest} to shipping balance"
                                  : AppStrings.withdrawalRequest,
                              overflow: TextOverflow.visible,
                              style: AppTextStyle.contentHeading0(
                                  textColor: AppColors.writingBlack0),
                            ),
                          ),
                          verticalSizedBox(5),

                          ///Order number and date time
                          Container(
                            alignment: Alignment.centerLeft,
                            // height: 25,
                            child: Text(
                              CommonMethods.dateTimeAmPm(
                                  date: "${transaction.date!} 00:00:00")[1],
                              style: AppTextStyle.smallText(
                                  textColor: AppColors.writingBlack1),
                            ),
                          )
                        ],
                      ),
                      verticalSizedBox(5),

                      ///Date and status
                    ],
                  ),
                ),
              ),

              ///Price and status
              Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    alignment: Alignment.center,
                    height: 20,
                    child: Text(
                      "₹${transaction.payoutAmount}",
                      overflow: TextOverflow.visible,
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack),
                    ),
                  ),

                  ///Success and faild
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      transaction.transactionStatus == AppStrings.success
                          ? AppStrings.successMessage
                          : transaction.transactionStatus == AppStrings.pending
                              ? AppStrings.pendingMessage
                              : AppStrings.failMessage,
                      style: AppTextStyle.smallText(
                        textColor:
                            transaction.transactionStatus == AppStrings.success
                                ? AppColors.brandBlack
                                : transaction.transactionStatus ==
                                        AppStrings.pending
                                    ? AppColors.orange
                                    : AppColors.red,
                      ),
                    ),
                  )
                ],
              )
            ],
          ),
        ),
        divider()
      ],
    );
  }
//endregion
}
