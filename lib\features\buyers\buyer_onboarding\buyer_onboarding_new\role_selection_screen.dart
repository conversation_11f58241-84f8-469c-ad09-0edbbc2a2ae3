import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_on_boarding/select_roles/selecte_roles.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/congratulations_screen.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/role_selection_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:flutter/services.dart';

class RoleSelectionScreen extends StatefulWidget {
  final String userReference;
  final Map<String, dynamic> userData;
  final String? icon;

  const RoleSelectionScreen({
    Key? key,
    required this.userReference,
    required this.userData,
    this.icon,
  }) : super(key: key);

  @override
  _RoleSelectionScreenState createState() => _RoleSelectionScreenState();
}

class _RoleSelectionScreenState extends State<RoleSelectionScreen> {
  //region Bloc
  late RoleSelectionBloc roleSelectionBloc;

  //endregion

  //region Init
  @override
  void initState() {
    roleSelectionBloc = RoleSelectionBloc(context, widget.userReference);
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: SafeArea(
        child: body(),
      ),
    );
  }

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 40),
            header(),
            const SizedBox(height: 30),
            profilePictureSection(),
            const SizedBox(height: 30),
            roleSelectionSection(),
            const SizedBox(height: 30),
            inviteCodeSection(),
            const SizedBox(height: 40),
            joinCommunityButton(),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Header
  Widget header() {
    return Text(
      "Great! Help us personalize Swadeshi experience for you!",
      style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
    );
  }
  //endregion

  //region Profile Picture Section
  Widget profilePictureSection() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Center(
          child: StreamBuilder<String>(
            stream: roleSelectionBloc.userProfilePictureCtrl.stream,
            initialData: null,
            builder: (context, snapshot) {
              return Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: AppColors.borderColor1),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(50),
                  child: snapshot.data == null
                      ? SvgPicture.asset(
                          AppImages.userPlaceHolder,
                          fit: BoxFit.cover,
                        )
                      : Image.file(
                          File(snapshot.data!),
                          fit: BoxFit.cover,
                        ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 8),
        Text(
          widget.userData["user_name"],
          style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 16),
        CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () {
            roleSelectionBloc.openGallery();
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.brandBlack,
              borderRadius: BorderRadius.circular(9),
            ),
            child: Text(
              "Upload profile picture",
              style: AppTextStyle.access0(textColor: AppColors.appWhite),
            ),
          ),
        ),
      ],
    );
  }
  //endregion

  //region Role Selection Section
  Widget roleSelectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                "How do you want to support the Swadeshi mission & ecosystem?",
                style:
                    AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
              ),
            ),
            AppToolTip(
              message: AppStrings.yourAnswareHelpUs,
              toolTipWidget: Text(
                AppStrings.why,
                style: AppTextStyle.contentText0(
                    textColor: AppColors.writingBlack1),
              ),
            ),
          ],
        ),
        Text(
          "(choose as many roles as you want)",
          style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
        ),
        const SizedBox(height: 16),
        SelectRoles(
          onChangeData: (value) {
            roleSelectionBloc.selectedRoles = value;
          },
        ),
      ],
    );
  }
  //endregion

  //region Invite Code Section
  Widget inviteCodeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                AppStrings.inviteCode,
                style:
                    AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
              ),
            ),
            AppToolTip(
              message: "Invite code benefits you with additional rewards and trust in the community",
              toolTipWidget: Text(
                "why?",
                style: AppTextStyle.contentText0(
                    textColor: AppColors.writingBlack1),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          autocorrect: false,
          enableSuggestions: false,
          controller: roleSelectionBloc.inviteCodeTextCtrl,
          decoration: InputDecoration(
            hintText: AppStrings.inviteCode,
            filled: true,
            fillColor: AppColors.textFieldFill1,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            hintStyle:
                AppTextStyle.hintText(textColor: AppColors.writingBlack1),
          ),
          onChanged: (val) {
            roleSelectionBloc.inviteCodeTextCtrl.text = val
                .replaceAll(RegExp('[^A-Za-z0-9]'), '')
                .toUpperCase();
            roleSelectionBloc.inviteCodeTextCtrl.selection =
                TextSelection.fromPosition(TextPosition(
                    offset: roleSelectionBloc.inviteCodeTextCtrl.text.length));
          },
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp('[A-Za-z0-9]')),
          ],
        ),
      ],
    );
  }
  //endregion

  //region Join Community Button
  Widget joinCommunityButton() {
    return SizedBox(
      width: double.infinity,
      child: CupertinoButton(
        borderRadius: BorderRadius.circular(10),
        padding: const EdgeInsets.symmetric(vertical: 14.5, horizontal: 15),
        color: AppColors.brandBlack,
        child: Text(
          "Join the Swadeshi Community",
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppTextStyle.access1(textColor: AppColors.appWhite),
        ),
        onPressed: () {
          if (roleSelectionBloc.validateRoles()) {
            // Combine user data with selected roles
            Map<String, dynamic> finalUserData = Map.from(widget.userData);
            finalUserData["user_roles"] = roleSelectionBloc.selectedRoles;

            if (roleSelectionBloc.inviteCodeTextCtrl.text.trim().isNotEmpty) {
              finalUserData["invited_by_code"] =
                  roleSelectionBloc.inviteCodeTextCtrl.text.trim();
            }

            // If user has selected a profile picture, upload it after creating the profile
            bool hasProfilePicture =
                roleSelectionBloc.userProfilePictureCtrl.hasListener &&
                    roleSelectionBloc.userProfilePicturePath != null;

            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CongratulationsScreen(
                  userReference: widget.userReference,
                  userData: finalUserData,
                  profilePicturePath: hasProfilePicture
                      ? roleSelectionBloc.userProfilePicturePath
                      : null,
                ),
              ),
            );
          }
        },
      ),
    );
  }
  //endregion
}
