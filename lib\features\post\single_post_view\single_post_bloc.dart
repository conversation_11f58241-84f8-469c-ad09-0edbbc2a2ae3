import 'dart:async';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/logout/logout.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_access_bottom_sheet/share_access_bottom_sheet.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/edit_post/edit_post_screen.dart';
import 'package:swadesic/features/post/single_post_view/commentr_field_bloc.dart';
import 'package:swadesic/features/post/single_post_view/single_post_comment_pagination.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/single_product_and_image_service/single_product_and_image_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum SinglePostViewState { Loading, Success, Failed, Empty }
enum CommentsState { Loading, Success, Failed, Empty }
enum CommentPaginationState { Loading, Success, Failed, Empty, End }

class SinglePostViewBloc {
  //region Common variable
  late BuildContext context;
  final String postProductCommentReference;
  late ScrollController scrollController = ScrollController();
  bool isPaginationEnded = false;
  bool visibleCommentField = false;
  bool showCommentFieldForReply = false; // New state for reply-triggered comment field
  final bool isFromProduct;
  int offset = 0;
  int limit = 10;
  late Product product;
  //Reply comment or post detail
  late Map<String,dynamic> replyCommentOrPostDetail;
  late SinglePostCommentPagination singlePostCommentPagination;
  List<PostDetail> commentList = [];
  late PostDetail singlePostOrComment;
  //Comment field bloc
  late CommentFieldsBloc commentFieldsBloc;
  //endregion

//region Text Editing Controller
//endregion

//region Controller
  final singlePostStateCtrl = StreamController<SinglePostViewState>.broadcast();
  final commentStateCtrl = StreamController<CommentsState>.broadcast();
  final commentedProductCtrl = StreamController<CommentsState>.broadcast();
  final commentFieldVisibilityCtrl = StreamController<bool>.broadcast();
  ValueNotifier<CommentPaginationState> commentPaginationStateCtrl = ValueNotifier(CommentPaginationState.Success);

//endregion
  //region Constructor
  SinglePostViewBloc(this.context, this.postProductCommentReference, this.isFromProduct,);

  //endregion
//region Init
  init() {
    //Comment field bloc initil
    commentFieldsBloc = CommentFieldsBloc(context,this);
    //Pagination initialization
    singlePostCommentPagination = SinglePostCommentPagination(context,this);
    //Add default reply or comment detail
    addDefaultReplyCommentDetail();
    //Get product info only if from Product comment screen
    getSingleProduct();
    //If from product screen then call only comments list else call post or comment
    if(isFromProduct){
      //Get root comments
      getCommentList();
    }
    else{
      getSinglePostOrComment();

    }
  }

//endregion

  //region On tap drawer
  void onTapDrawer({required PostDetail postDetail})async{
    List<Map<String, dynamic>> accessOptions = [];
    if(postDetail.createdBy!.userOrStoreReference ==
        (AppConstants.appData.isUserView!
            ?AppConstants.appData.userReference!
            :AppConstants.appData.storeReference!))
    {
      accessOptions =

      [
        //Copy
        {
          'title': AppStrings.copyLink,
          'onTap': () {
            Navigator.pop(context);
            CommonMethods.copyText(context, AppLinkCreateService().createPostLink(postReference: postDetail.postOrCommentReference!));
          },
        },
        //Edit
        {
          'title': AppStrings.edit,
          'onTap': () {
            Navigator.pop(context);
            goToEditPost(postDetail: postDetail);


          },
        },
        //Delete post
        {
          'title': AppStrings.delete,
          'onTap': () {
            Navigator.pop(context);
            confirmDelete(postDetail: postDetail);

          },
        },
        // Add more options if needed
      ];
    }
    else{
      accessOptions = [
        {
          'title': AppStrings.report,
          'onTap': () {
            Navigator.pop(context);
            // Navigator.pop(context);
            var screen = ReportScreen(
              reference: postDetail.postOrCommentReference!,
              isPostComment: true,
            );
            var route = MaterialPageRoute(builder: (context) => screen);
            Navigator.push(context, route);

//endregion

          },
        },
      ];

    }

    CommonMethods.accessBottomSheet(screen: ShareAccessBottomSheet(accessOptions: accessOptions), context: context,);

  }
  //endregion

  //region Get single post or comment
  Future<void>getSinglePostOrComment()async{

    try{
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      // First, try to get the data from PostDataModel (preserves review data from store reviews screen)
      var existingPosts = postDataModel.allPostDetailList
          .where((element) => element.postOrCommentReference == postProductCommentReference)
          .toList();

      if(existingPosts.isNotEmpty) {
        // Use existing data from PostDataModel (includes reviewed products for reviews)
        singlePostOrComment = existingPosts.first;
        print("SinglePostViewBloc: Using existing data for $postProductCommentReference");
        print("SinglePostViewBloc: Review data preserved - reviewed products: ${singlePostOrComment.reviewedProducts?.length ?? 0}");
      } else {
        print("SinglePostViewBloc: Data not found in PostDataModel, making API call for $postProductCommentReference");
        // Fallback to API call if data not found in PostDataModel

        //If from product
        if(isFromProduct){
          // Handle product-specific logic if needed
        }
        //If post
        else if(postProductCommentReference.startsWith("PO")){
          singlePostOrComment = await PostService().getSinglePost(postReference: postProductCommentReference);
        }
        else{
          singlePostOrComment = await PostService().getSingleComment(commentReference: postProductCommentReference);
        }

        //Add data in post data model
        postDataModel.addPostIntoList(postList:[singlePostOrComment]);
      }

      // Ensure the data is available in PostDataModel before setting success state
      // This fixes the race condition where Consumer checks before data is available
      if (!postDataModel.allPostDetailList.any((element) => element.postOrCommentReference == postProductCommentReference)) {
        postDataModel.addPostIntoList(postList:[singlePostOrComment]);
      }

      // Force UI update to ensure Consumer sees the data
      postDataModel.updateUi();

      // Small delay to ensure Consumer has updated before setting success state
      await Future.delayed(const Duration(milliseconds: 50));

      //Success - set state after ensuring data is in PostDataModel
      singlePostStateCtrl.sink.add(SinglePostViewState.Success);

      //Get root comments (async, don't wait for it)
      getCommentList();
    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      singlePostStateCtrl.sink.add(SinglePostViewState.Failed);
      return;
    }
    catch(error){
      //Failed
      singlePostStateCtrl.sink.add(SinglePostViewState.Failed);
      return;
    }
  }
//endregion

  //region Get comment list
  Future<void>getCommentList()async{
    try{
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Api call
      commentList = await PostService().getCommentList(offset: 0,limit: 10,parentReference: postProductCommentReference);
      //Remove deleted comments
      commentList.removeWhere((element) => element.isDeleted!);
      //Add data in post data model
      postDataModel.addPostIntoList(postList:commentList);
      //If Current post reference is not in the list then return Empty
      // if(postDataModel.allPostDetailList.where((element) => element.postOrCommentReference == postReference).isEmpty){
      //   return commentStateCtrl.sink.add(CommentsState.Empty);
      // }
      //Success
      commentStateCtrl.sink.add(CommentsState.Success);
    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      //Failed
      commentStateCtrl.sink.add(CommentsState.Failed);
      return;
    }
    catch(error){
      //Failed
      commentStateCtrl.sink.add(CommentsState.Failed);
      return;
    }
  }
//endregion

  //region On tap user or store icon
  void onTapUserOrStoreIcon({required String reference}) {
    late Widget screen;

    //Admin user view
    if (reference == AppConstants.appData.userReference && AppConstants.appData.isUserView!) {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }
    //Admin store view
    else if (reference == AppConstants.appData.storeReference && AppConstants.appData.isStoreView!) {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
        isStoreOwnerView: true,
      );
    }
    //If normal store
    else if (reference.split("").first == "S") {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
      );
    }
    //Else normal user view
    else {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }

    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion

  //region Convert date
  String convertDateFormat({required String inputDateTimeString}) {
    DateTime dateTime = DateTime.parse(inputDateTimeString).toLocal();

    // Formatting the date and time
    String formattedDateTime = DateFormat('h:mma MMM d,yyyy').format(dateTime);

    return formattedDateTime;
  }

  //endregion

  //region On tap image
  void onTapImage({required List<String> imageList, required int index}) {
    Widget screen = BuyerImagePreviewScreen(
      productImage: imageList,
      imageIndex: index,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);
  }

  //endregion

  //region Delete post api call
  Future<void> deletePostAndComment({required PostDetail postDetail}) async {
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //Delete status
    bool isSuccess = false;
    //region Try
    try {
      //Api call
      //If post
      if( postDetail.postOrCommentReference!.startsWith("P")){
        isSuccess = await PostService().deletePost(postRefrence: postDetail.postOrCommentReference!);
      }
      else{
        isSuccess = await PostService().deleteComment(postRefrence: postDetail.postOrCommentReference!);
      }
      //If is success is false then return
      if(!isSuccess){
        context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
        return;
      }
      //Remove local data
      //Remove post detail from the userOrStoreFeeds
      // postDataModel.userOrStoreFeedsList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Remove post detail from the allPostDetailList
      postDataModel.allPostDetailList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);

     //Reduce the parent comment count
      var data = postDataModel.allPostDetailList.firstWhere((element) => element.postOrCommentReference == postProductCommentReference);
      data.commentCount =  data.commentCount! - 1;
      //Refresh ui
      postDataModel.updateUi();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
//endregion

  //region On tap heart
  Future<void>onTapHeart({required PostDetail postDetail})async{
    try{
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      //Update liked count and is liked
      //Toggle the like status
      postDetail.likeStatus = !postDetail.likeStatus!;
      if(postDetail.likeStatus!){
        postDetail.likeCount = (postDetail.likeCount ?? 0) + 1;
      }
      else{
        postDetail.likeCount = (postDetail.likeCount ?? 0) - 1;
      }
      //Refresh ui
      postDataModel.updateUi();
      //Api call
      await PostService().likePost(postReference: postDetail.postOrCommentReference!, likeStatus: postDetail.likeStatus!);
    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      //Failed
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
      return;
    }
  }
//endregion

  //region Go to edit post
  void goToEditPost({required PostDetail postDetail}){
    Widget screen= EditPostScreen(postDetail: postDetail,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route).then((value) {
      // singlePostStateCtrl.sink.add(SinglePostViewState.Success);
      // getSinglePostOrComment();
    });
  }
  //endregion

  //region Go to single post view
  void goToSinglePostView({required String postReference}){
    var screen = SinglePostViewScreen(postReference: postReference,isFromProductScreen: !postReference.startsWith("PO") && !postReference.startsWith("CO"),);
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region Confirm delete
  Future confirmDelete({required PostDetail postDetail}){
    return CommonMethods.appDialogBox(
        context: context,
        widget:
        OkayAndCancelDialogScreen(onTapSecondButton:(){
          deletePostAndComment(postDetail: postDetail);
        },previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.areYouSureWantsToDelete,
          firstButtonName: "Cancel",
          secondButtonName: "Delete",

        )
    );
  }
//endregion

  //region On Tap Share
  void onTapShare({required PostDetail postDetail}) {
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        entityType: EntityType.POST,
        url:AppLinkCreateService().createPostLink(postReference: postDetail.postOrCommentReference!,),
        imageLink:postDetail.images!.isEmpty?null:postDetail.images!.first.mediaPath,
        imageType: CustomImageContainerType.post,
        postText: postDetail.text,
        postCreatorName: postDetail.createdBy?.handle,
        postCreatorIcon: postDetail.createdBy?.icon,
        objectReference: postDetail.postOrCommentReference,
    ), context: context,);
    //
    // showModalBottomSheet(
    //     context: context,
    //     isScrollControlled: true,
    //     enableDrag: true,
    //     backgroundColor: AppColors.appWhite,
    //     shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topRight: Radius.circular(20), topLeft: Radius.circular(20))),
    //     builder: (context) {
    //       return SingleChildScrollView(
    //           padding: EdgeInsets.zero,
    //           child: Container(
    //             child: Column(
    //               children: [
    //                 Container(
    //                   margin: const EdgeInsets.symmetric(vertical: 20),
    //                   child: Column(
    //                     children: [
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                       verticalSizedBox(10),
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                     ],
    //                   ),
    //                 ),
    //                 BuyerProductShareScreen(url: link, imageLink:imageUrl,),
    //               ],
    //             ),
    //           ));
    //     }).then((value) {
    //   // if (value == null) return;
    //   // supportFilterModel = value;
    //   // applyFilter();
    // });
  }
  //endregion

  //region Single product Api Call
  Future<void>getSingleProduct() async {
    try {
      //if not from product then return
      if(!isFromProduct){
        return;
      }
      commentedProductCtrl.sink.add(CommentsState.Loading);
      var onlyProductInfo = await SingleProductAndImageService().getSingleProductInfo(productReference: postProductCommentReference, pinCode: '000000');
      ///After getting detail add to final product
      product = onlyProductInfo.singleProduct!;
     //Get product images
      var onlyProductImage = await SingleProductAndImageService().getSingleProductImage(postProductCommentReference,"");
      ///After getting detail add to final product
      product.prodImages = onlyProductImage.data!;
      // singleProductViewCtrl.sink.add(BuyerViewSingleProductState.Success);
      //Success
      commentedProductCtrl.sink.add(CommentsState.Success);
      //print("Product info is fetched and product name is ${product.productName}");

    }   on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      commentedProductCtrl.sink.add(CommentsState.Failed);
      return;
    }
  }

//endregion

  //region On tap product
  void onTapProduct({required String reference}){
    var screen =  BuyerViewSingleProductScreen(productReference: reference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);


  }
//endregion


  ///Reply
  //region Add default reply comment detail
  void addDefaultReplyCommentDetail(){
    replyCommentOrPostDetail =
    {
      "reference": postProductCommentReference,
      "handle":""
    };
  }
  //endregion

  //region On tap reply for comment
  void onTapReplyForComment() {
    // Set reply details for the current comment
    replyCommentOrPostDetail = {
      "reference": singlePostOrComment.postOrCommentReference!,
      "handle": singlePostOrComment.createdBy!.handle!
    };

    // Show comment field for reply
    showCommentFieldForReply = true;
    commentFieldVisibilityCtrl.sink.add(true);

    // Trigger reply in comment field bloc
    commentFieldsBloc.onTapReply(replyCommentOrPostDetail: replyCommentOrPostDetail);
  }
  //endregion




//region Dispose
  void dispose() {
    singlePostStateCtrl.close();
    commentStateCtrl.close();
    commentedProductCtrl.close();
    commentPaginationStateCtrl.dispose();
  }
//endregion
}
